使用全部5批数据，混在一起，

seed=42
最终结果:
训练集准确率: 0.6215, F1分数: 0.5752
验证集准确率: 0.6066, F1分数: 0.5537
测试集准确率: 0.6406, F1分数: 0.6059
测试集预测结果已保存到 gcn_test_predictions.csv

seed=41
最终结果:
训练集准确率: 0.6356, F1分数: 0.6166
验证集准确率: 0.6885, F1分数: 0.6588
测试集准确率: 0.6094, F1分数: 0.6015
测试集预测结果已保存到 gcn_test_predictions.csv

seed=40
最终结果:
训练集准确率: 0.6255, F1分数: 0.6255
验证集准确率: 0.6393, F1分数: 0.6369
测试集准确率: 0.5625, F1分数: 0.5621
测试集预测结果已保存到 gcn_test_predictions.csv

seed=40
====================================================================================================
四模型性能比较结果
====================================================================================================
    Model Test Accuracy Test F1 Score Training Time (s)    Status
      GCN        0.5625        0.5152             47.62 ✓ Success
      GAT        0.5938        0.5836            160.94 ✓ Success
GraphSAGE        0.5938        0.5789             36.35 ✓ Success
      GIN        0.5938        0.5789             38.38 ✓ Success

比较结果已保存到 four_models_comparison_results.csv
综合比较图表已保存到 four_models_comprehensive_comparison.png

====================================================================================================
深度性能分析
====================================================================================================
🏆 最高准确率: GAT (0.5938)
🏆 最高F1分数: GAT (0.5836)
⚡ 最快训练: GraphSAGE (36.35秒)

📊 模型特征对比:

GCN:
  - 理论表达能力: 中等
  - 模型复杂度: 低
  - 内存需求: 低
  - 可解释性: 中等
  - 可扩展性: 中等
  - 最适用于: 基础图分类，计算资源有限

GAT:
  - 理论表达能力: 中等
  - 模型复杂度: 高
  - 内存需求: 高
  - 可解释性: 高
  - 可扩展性: 中等
  - 最适用于: 需要注意力权重解释

GraphSAGE:
  - 理论表达能力: 中等
  - 模型复杂度: 中等
  - 内存需求: 中等
  - 可解释性: 中等
  - 可扩展性: 高
  - 最适用于: 大规模图数据，归纳学习

GIN:
  - 理论表达能力: 最强
  - 模型复杂度: 高
  - 内存需求: 高
  - 可解释性: 低
  - 可扩展性: 中等
  - 最适用于: 复杂图结构，理论研究

📈 性能统计:
准确率 - 均值: 0.5859, 标准差: 0.0135
F1分数 - 均值: 0.5642, 标准差: 0.0284
训练时间 - 均值: 70.82s, 标准差: 52.20s

💡 模型选择建议:
  - 各模型性能差异明显，建议选择性能最佳的模型
  - 当前最佳模型: GAT

🎯 应用场景推荐:
  - 临床快速诊断: GCN (速度优先)
  - 研究分析: GAT (可解释性)
  - 大规模筛查: GraphSAGE (可扩展性)
  - 理论研究: GIN (最强表达能力)

====================================================================================================
四模型比较完成！