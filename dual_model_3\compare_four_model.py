import time
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from train_gcn_model import train_val_test_split as gcn_train_val_test
from train_gat_model import train_val_test_split as gat_train_val_test
from train_graphsage_model import train_val_test_split as graphsage_train_val_test
from train_gin_model import train_val_test_split as gin_train_val_test

def compare_four_models():
    """
    比较GCN、GAT、GraphSAGE和GIN四个模型的性能
    """
    print("=" * 100)
    print("开始比较GCN、GAT、GraphSAGE和GIN四个模型性能")
    print("=" * 100)
    
    results = {}
    model_functions = {
        'GCN': gcn_train_val_test,
        'GAT': gat_train_val_test,
        'GraphSAGE': graphsage_train_val_test,
        'GIN': gin_train_val_test
    }
    
    # 训练每个模型
    for model_name, train_func in model_functions.items():
        print(f"\n" + "=" * 50)
        print(f"训练{model_name}模型")
        print("=" * 50)
        
        start_time = time.time()
        try:
            test_acc, test_f1 = train_func()
            training_time = time.time() - start_time
            
            results[model_name] = {
                'test_accuracy': test_acc,
                'test_f1': test_f1,
                'training_time': training_time,
                'status': 'success'
            }
            print(f"\n✓ {model_name}训练完成，用时: {training_time:.2f}秒")
            
        except Exception as e:
            training_time = time.time() - start_time
            results[model_name] = {
                'test_accuracy': 0.0,
                'test_f1': 0.0,
                'training_time': training_time,
                'status': 'failed',
                'error': str(e)
            }
            print(f"\n✗ {model_name}训练失败: {str(e)}")
    
    # 生成比较报告
    generate_comparison_report(results)
    
    # 创建可视化图表
    create_comprehensive_visualizations(results)
    
    # 深度分析
    perform_deep_analysis(results)
    
    return results

def generate_comparison_report(results):
    """
    生成详细的比较报告
    """
    print("\n" + "=" * 100)
    print("四模型性能比较结果")
    print("=" * 100)
    
    # 创建比较表格
    comparison_data = []
    for model_name, metrics in results.items():
        if metrics['status'] == 'success':
            comparison_data.append({
                'Model': model_name,
                'Test Accuracy': f"{metrics['test_accuracy']:.4f}",
                'Test F1 Score': f"{metrics['test_f1']:.4f}",
                'Training Time (s)': f"{metrics['training_time']:.2f}",
                'Status': '✓ Success'
            })
        else:
            comparison_data.append({
                'Model': model_name,
                'Test Accuracy': 'Failed',
                'Test F1 Score': 'Failed',
                'Training Time (s)': f"{metrics['training_time']:.2f}",
                'Status': f"✗ Failed"
            })
    
    comparison_df = pd.DataFrame(comparison_data)
    print(comparison_df.to_string(index=False))
    
    # 保存比较结果
    comparison_df.to_csv('four_models_comparison_results.csv', index=False)
    print(f"\n比较结果已保存到 four_models_comparison_results.csv")

def create_comprehensive_visualizations(results):
    """
    创建综合性能可视化图表
    """
    # 过滤成功的模型
    successful_models = {k: v for k, v in results.items() if v['status'] == 'success'}
    
    if len(successful_models) < 2:
        print("⚠ 成功训练的模型少于2个，跳过可视化")
        return
    
    # 设置图表样式
    plt.style.use('default')
    fig = plt.figure(figsize=(20, 15))
    
    models = list(successful_models.keys())
    accuracies = [successful_models[model]['test_accuracy'] for model in models]
    f1_scores = [successful_models[model]['test_f1'] for model in models]
    training_times = [successful_models[model]['training_time'] for model in models]
    
    colors = ['skyblue', 'lightcoral', 'lightgreen', 'gold'][:len(models)]
    
    # 1. 准确率比较 (2x3布局的第1个)
    ax1 = plt.subplot(2, 3, 1)
    bars1 = ax1.bar(models, accuracies, color=colors)
    ax1.set_title('Test Accuracy Comparison', fontweight='bold', fontsize=14)
    ax1.set_ylabel('Accuracy')
    ax1.set_ylim(0, 1)
    ax1.tick_params(axis='x', rotation=45)
    for i, v in enumerate(accuracies):
        ax1.text(i, v + 0.01, f'{v:.4f}', ha='center', va='bottom', fontweight='bold')
    
    # 2. F1分数比较
    ax2 = plt.subplot(2, 3, 2)
    bars2 = ax2.bar(models, f1_scores, color=colors)
    ax2.set_title('Test F1 Score Comparison', fontweight='bold', fontsize=14)
    ax2.set_ylabel('F1 Score')
    ax2.set_ylim(0, 1)
    ax2.tick_params(axis='x', rotation=45)
    for i, v in enumerate(f1_scores):
        ax2.text(i, v + 0.01, f'{v:.4f}', ha='center', va='bottom', fontweight='bold')
    
    # 3. 训练时间比较
    ax3 = plt.subplot(2, 3, 3)
    bars3 = ax3.bar(models, training_times, color=colors)
    ax3.set_title('Training Time Comparison', fontweight='bold', fontsize=14)
    ax3.set_ylabel('Time (seconds)')
    ax3.tick_params(axis='x', rotation=45)
    for i, v in enumerate(training_times):
        ax3.text(i, v + max(training_times) * 0.01, f'{v:.1f}s', ha='center', va='bottom', fontweight='bold')
    
    # 4. 综合性能雷达图
    ax4 = plt.subplot(2, 3, 4, projection='polar')
    
    # 归一化指标用于雷达图
    max_acc = max(accuracies) if accuracies else 1
    max_f1 = max(f1_scores) if f1_scores else 1
    max_time = max(training_times) if training_times else 1
    
    # 计算效率分数（时间越短分数越高）
    efficiency_scores = [(max_time - t) / max_time for t in training_times]
    
    # 雷达图数据
    categories = ['Accuracy', 'F1 Score', 'Efficiency']
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形
    
    ax4.set_theta_offset(np.pi / 2)
    ax4.set_theta_direction(-1)
    ax4.set_thetagrids(np.degrees(angles[:-1]), categories)
    
    for i, model in enumerate(models):
        values = [accuracies[i], f1_scores[i], efficiency_scores[i]]
        values += values[:1]  # 闭合图形
        
        ax4.plot(angles, values, 'o-', linewidth=2, label=model, color=colors[i])
        ax4.fill(angles, values, alpha=0.25, color=colors[i])
    
    ax4.set_ylim(0, 1)
    ax4.set_title('Comprehensive Performance Radar', fontweight='bold', fontsize=14)
    ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    # 5. 性能-效率散点图
    ax5 = plt.subplot(2, 3, 5)
    scatter = ax5.scatter(training_times, accuracies, c=range(len(models)), 
                         s=200, alpha=0.7, cmap='viridis')
    
    for i, model in enumerate(models):
        ax5.annotate(model, (training_times[i], accuracies[i]), 
                    xytext=(5, 5), textcoords='offset points', fontweight='bold')
    
    ax5.set_xlabel('Training Time (seconds)')
    ax5.set_ylabel('Test Accuracy')
    ax5.set_title('Performance vs Efficiency', fontweight='bold', fontsize=14)
    ax5.grid(True, alpha=0.3)
    
    # 6. 模型复杂度对比
    ax6 = plt.subplot(2, 3, 6)
    
    # 模型复杂度评分（主观评分，1-5分）
    complexity_scores = {
        'GCN': 2,
        'GAT': 4,
        'GraphSAGE': 3,
        'GIN': 5
    }
    
    model_complexities = [complexity_scores.get(model, 3) for model in models]
    
    bars6 = ax6.bar(models, model_complexities, color=colors)
    ax6.set_title('Model Complexity Comparison', fontweight='bold', fontsize=14)
    ax6.set_ylabel('Complexity Score (1-5)')
    ax6.set_ylim(0, 5)
    ax6.tick_params(axis='x', rotation=45)
    
    for i, v in enumerate(model_complexities):
        ax6.text(i, v + 0.1, f'{v}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('four_models_comprehensive_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"综合比较图表已保存到 four_models_comprehensive_comparison.png")

def perform_deep_analysis(results):
    """
    执行深度性能分析
    """
    print("\n" + "=" * 100)
    print("深度性能分析")
    print("=" * 100)
    
    successful_models = {k: v for k, v in results.items() if v['status'] == 'success'}
    
    if len(successful_models) == 0:
        print("⚠ 没有成功训练的模型，无法进行分析")
        return
    
    # 找出最佳模型
    best_acc_model = max(successful_models.items(), key=lambda x: x[1]['test_accuracy'])
    best_f1_model = max(successful_models.items(), key=lambda x: x[1]['test_f1'])
    fastest_model = min(successful_models.items(), key=lambda x: x[1]['training_time'])
    
    print(f"🏆 最高准确率: {best_acc_model[0]} ({best_acc_model[1]['test_accuracy']:.4f})")
    print(f"🏆 最高F1分数: {best_f1_model[0]} ({best_f1_model[1]['test_f1']:.4f})")
    print(f"⚡ 最快训练: {fastest_model[0]} ({fastest_model[1]['training_time']:.2f}秒)")
    
    # 模型特征分析
    print(f"\n📊 模型特征对比:")
    
    model_characteristics = {
        'GCN': {
            'theoretical_power': '中等',
            'complexity': '低',
            'memory': '低',
            'interpretability': '中等',
            'scalability': '中等',
            'best_for': '基础图分类，计算资源有限'
        },
        'GAT': {
            'theoretical_power': '中等',
            'complexity': '高',
            'memory': '高',
            'interpretability': '高',
            'scalability': '中等',
            'best_for': '需要注意力权重解释'
        },
        'GraphSAGE': {
            'theoretical_power': '中等',
            'complexity': '中等',
            'memory': '中等',
            'interpretability': '中等',
            'scalability': '高',
            'best_for': '大规模图数据，归纳学习'
        },
        'GIN': {
            'theoretical_power': '最强',
            'complexity': '高',
            'memory': '高',
            'interpretability': '低',
            'scalability': '中等',
            'best_for': '复杂图结构，理论研究'
        }
    }
    
    for model_name in successful_models.keys():
        if model_name in model_characteristics:
            chars = model_characteristics[model_name]
            print(f"\n{model_name}:")
            print(f"  - 理论表达能力: {chars['theoretical_power']}")
            print(f"  - 模型复杂度: {chars['complexity']}")
            print(f"  - 内存需求: {chars['memory']}")
            print(f"  - 可解释性: {chars['interpretability']}")
            print(f"  - 可扩展性: {chars['scalability']}")
            print(f"  - 最适用于: {chars['best_for']}")
    
    # 性能统计分析
    if len(successful_models) >= 2:
        acc_values = [v['test_accuracy'] for v in successful_models.values()]
        f1_values = [v['test_f1'] for v in successful_models.values()]
        time_values = [v['training_time'] for v in successful_models.values()]
        
        print(f"\n📈 性能统计:")
        print(f"准确率 - 均值: {np.mean(acc_values):.4f}, 标准差: {np.std(acc_values):.4f}")
        print(f"F1分数 - 均值: {np.mean(f1_values):.4f}, 标准差: {np.std(f1_values):.4f}")
        print(f"训练时间 - 均值: {np.mean(time_values):.2f}s, 标准差: {np.std(time_values):.2f}s")
    
    # 推荐建议
    print(f"\n💡 模型选择建议:")
    
    if len(successful_models) >= 2:
        acc_values = [v['test_accuracy'] for v in successful_models.values()]
        acc_std = np.std(acc_values)
        
        if acc_std < 0.01:
            print("  - 各模型性能相近，建议根据具体需求选择：")
            print("    * 追求简单高效 → GCN")
            print("    * 需要可解释性 → GAT")
            print("    * 处理大规模数据 → GraphSAGE")
            print("    * 探索理论极限 → GIN")
        else:
            print("  - 各模型性能差异明显，建议选择性能最佳的模型")
            print(f"  - 当前最佳模型: {best_acc_model[0]}")
    
    print("\n🎯 应用场景推荐:")
    print("  - 临床快速诊断: GCN (速度优先)")
    print("  - 研究分析: GAT (可解释性)")
    print("  - 大规模筛查: GraphSAGE (可扩展性)")
    print("  - 理论研究: GIN (最强表达能力)")

def main():
    """
    主函数
    """
    try:
        results = compare_four_models()
        print("\n" + "=" * 100)
        print("四模型比较完成！")
        print("=" * 100)
        print("生成的文件:")
        print("- four_models_comparison_results.csv: 详细比较结果")
        print("- four_models_comprehensive_comparison.png: 综合性能比较图表")
        print("- best_gcn_model.pt: 最佳GCN模型")
        print("- best_gat_model.pt: 最佳GAT模型")
        print("- best_graphsage_model.pt: 最佳GraphSAGE模型")
        print("- best_gin_model.pt: 最佳GIN模型")
        print("- *_test_predictions.csv: 各模型测试预测")
        print("- *_test_confusion_matrix.png: 各模型混淆矩阵")
        
    except Exception as e:
        print(f"比较过程中出现错误: {str(e)}")
        print("请检查数据文件是否存在以及依赖包是否正确安装")

if __name__ == "__main__":
    main()