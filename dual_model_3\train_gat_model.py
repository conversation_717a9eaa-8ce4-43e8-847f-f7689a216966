import os
import glob
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GATConv, global_mean_pool
from torch_geometric.data import Data, DataLoader
from sklearn.metrics import f1_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.io as sio

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

class GAT(torch.nn.Module):
    """
    图注意力网络模型 (Graph Attention Network)
    """
    def __init__(self, num_node_features, hidden_channels, num_classes, heads=8, dropout=0.2):
        super(GAT, self).__init__()
        
        # 第一层图注意力卷积
        self.conv1 = GATConv(num_node_features, hidden_channels, heads=heads, dropout=dropout)
        
        # 第二层图注意力卷积
        self.conv2 = GATConv(hidden_channels * heads, hidden_channels, heads=heads, dropout=dropout)
        
        # 第三层图注意力卷积
        self.conv3 = GATConv(hidden_channels * heads, hidden_channels, heads=1, dropout=dropout)

        # 分类层
        self.lin = nn.Linear(hidden_channels, num_classes)
        
        self.dropout = dropout

    def forward(self, x, edge_index, batch):
        # 第一层图注意力卷积 + 激活函数
        x = self.conv1(x, edge_index)
        x = F.elu(x)
        x = F.dropout(x, p=self.dropout, training=self.training)

        # 第二层图注意力卷积 + 激活函数
        x = self.conv2(x, edge_index)
        x = F.elu(x)
        x = F.dropout(x, p=self.dropout, training=self.training)

        # 第三层图注意力卷积 + 激活函数
        x = self.conv3(x, edge_index)
        x = F.elu(x)
        x = F.dropout(x, p=self.dropout, training=self.training)

        # 图池化，计算每个图的平均节点特征
        x = global_mean_pool(x, batch)

        # 分类层
        x = self.lin(x)

        return x

def load_node_features(subject_id, label, split_dir=''):
    """
    加载特定被试的节点特征

    参数:
    - subject_id: 被试ID
    - label: 被试标签 (0=HC, 1=MCI)
    - split_dir: 数据集划分目录 (train/val/test)

    返回:
    - node_features: 节点特征矩阵
    """
    # 根据标签确定特征文件路径
    if label == 0:  # HC (健康对照组)
        if split_dir:
            feature_file = os.path.join('dataset_splits', split_dir, 'node_features', 'HC', f'ROISignals_{subject_id}.mat')
        else:
            feature_file = os.path.join('node_features', 'HC', f'ROISignals_{subject_id}.mat')
    elif label == 1:  # MCI (轻度认知障碍)
        if split_dir:
            feature_file = os.path.join('dataset_splits', split_dir, 'node_features', 'MCI', f'ROISignals_{subject_id}.mat')
        else:
            feature_file = os.path.join('node_features', 'MCI', f'ROISignals_{subject_id}.mat')
    else:
        raise ValueError(f"未知的标签类型: {label}")

    # 检查文件是否存在
    if not os.path.exists(feature_file):
        raise FileNotFoundError(f"节点特征文件不存在: {feature_file}")

    # 加载.mat文件
    try:
        mat_data = sio.loadmat(feature_file)

        # 提取ROISignals矩阵
        if 'ROISignals' in mat_data:
            signals = mat_data['ROISignals']  # 形状应为 [time_points, n_regions]

            # 计算每个节点的特征 (使用时间序列统计特征)
            # 这里使用均值、标准差、最小值、最大值作为特征
            mean = np.mean(signals, axis=0).reshape(-1, 1)  # [n_regions, 1]
            std = np.std(signals, axis=0).reshape(-1, 1)    # [n_regions, 1]
            min_val = np.min(signals, axis=0).reshape(-1, 1)  # [n_regions, 1]
            max_val = np.max(signals, axis=0).reshape(-1, 1)  # [n_regions, 1]

            # 组合特征 [n_regions, 4]
            features = np.hstack([mean, std, min_val, max_val])

            return features
        else:
            print(f"警告: 在文件 {feature_file} 中找不到ROISignals数据")
            return None

    except Exception as e:
        print(f"读取文件 {feature_file} 时出错: {str(e)}")
        return None

def load_split_data(split_name):
    """
    从数据集划分中加载特定划分的数据

    参数:
    - split_name: 划分名称 ('train', 'val', 'test')

    返回:
    - graph_data: 包含所有图数据的列表
    - labels: 对应的标签
    - subject_ids: 对应的被试ID
    """
    # 读取标签文件
    label_file = 'edge_lists/subject_labels.csv'
    if not os.path.exists(label_file):
        raise FileNotFoundError(f"标签文件不存在: {label_file}")

    label_df = pd.read_csv(label_file)

    # 获取指定划分的gcn_data文件
    hc_files = sorted(glob.glob(f'dataset_splits/{split_name}/edge_lists/HC/*_gcn_data.npz'))
    mci_files = sorted(glob.glob(f'dataset_splits/{split_name}/edge_lists/MCI/*_gcn_data.npz'))
    all_files = hc_files + mci_files

    print(f"加载 {split_name} 集数据，找到 {len(all_files)} 个文件")

    graph_data = []
    labels = []
    subject_ids = []

    # 处理每个文件
    for i, file_path in enumerate(all_files):
        # 提取被试ID
        subject_id = os.path.basename(file_path).split('_gcn_data.npz')[0]

        # 查找该被试的标签
        try:
            subject_id_int = int(subject_id)
        except ValueError:
            print(f"警告: 无法将被试ID {subject_id} 转换为整数，跳过")
            continue

        subject_label = label_df[label_df['subject_id'] == subject_id_int]['label'].values
        if len(subject_label) == 0:
            print(f"警告: 未找到被试 {subject_id_int} 的标签，跳过")
            continue
        label = subject_label[0]

        # 加载NPZ文件中的边数据
        npz_data = np.load(file_path)
        edge_index = torch.tensor(npz_data['edge_index'].T, dtype=torch.long)  # PyG期望[2, E]形状
        edge_attr = torch.tensor(npz_data['edge_attr'], dtype=torch.float)

        # 获取节点数量
        num_nodes = edge_index.max().item() + 1

        # 加载节点特征
        node_features = load_node_features(subject_id, label, split_name)

        if node_features is not None:
            # 确保节点数量匹配
            if node_features.shape[0] != num_nodes:
                print(f"警告: 被试 {subject_id} 的节点数不匹配，特征数: {node_features.shape[0]}，图中节点数: {num_nodes}")
                # 处理不匹配的情况
                if node_features.shape[0] > num_nodes:
                    # 截断多余的节点特征
                    node_features = node_features[:num_nodes, :]
                else:
                    # 不足的节点特征填充为0
                    padding = np.zeros((num_nodes - node_features.shape[0], node_features.shape[1]))
                    node_features = np.vstack([node_features, padding])

            # 转换为PyTorch张量
            x = torch.tensor(node_features, dtype=torch.float)
        else:
            # 如果无法加载节点特征，使用默认特征（节点度）
            print(f"警告: 使用节点度作为被试 {subject_id} 的默认节点特征")
            # 计算每个节点的度
            degrees = torch.zeros(num_nodes, dtype=torch.float)
            for node in edge_index[0]:
                degrees[node] += 1
            x = degrees.view(-1, 1)

        # 创建PyTorch Geometric的Data对象
        data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr, y=torch.tensor([label], dtype=torch.long))

        # 添加到数据列表
        graph_data.append(data)
        labels.append(label)
        subject_ids.append(subject_id)

    return graph_data, np.array(labels), subject_ids

def train_model(model, train_loader, optimizer, criterion, device):
    """
    训练模型
    """
    model.train()

    total_loss = 0
    for data in train_loader:
        data = data.to(device)
        optimizer.zero_grad()
        out = model(data.x, data.edge_index, data.batch)
        loss = criterion(out, data.y)
        loss.backward()
        optimizer.step()
        total_loss += loss.item() * data.num_graphs

    return total_loss / len(train_loader.dataset)

def evaluate_model(model, loader, device):
    """
    评估模型
    """
    model.eval()
    correct = 0
    predictions = []
    targets = []

    with torch.no_grad():
        for data in loader:
            data = data.to(device)
            out = model(data.x, data.edge_index, data.batch)
            pred = out.argmax(dim=1)
            correct += int((pred == data.y).sum())
            predictions.extend(pred.cpu().numpy())
            targets.extend(data.y.cpu().numpy())

    accuracy = correct / len(loader.dataset)
    f1 = f1_score(targets, predictions, average='macro')

    return accuracy, f1, predictions, targets

def train_val_test_split():
    """
    使用训练集/验证集/测试集评估GAT模型
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 加载各个划分的数据
    print("加载训练集数据...")
    train_data, train_labels, train_subject_ids = load_split_data('train')
    print("加载验证集数据...")
    val_data, val_labels, val_subject_ids = load_split_data('val')
    print("加载测试集数据...")
    test_data, test_labels, test_subject_ids = load_split_data('test')

    # 获取节点特征维度
    num_node_features = train_data[0].x.shape[1]
    print(f"节点特征维度: {num_node_features}")

    print(f"数据集大小: 训练集 {len(train_data)}, 验证集 {len(val_data)}, 测试集 {len(test_data)}")

    # 创建数据加载器
    train_loader = DataLoader(train_data, batch_size=16, shuffle=True)
    val_loader = DataLoader(val_data, batch_size=16, shuffle=False)
    test_loader = DataLoader(test_data, batch_size=16, shuffle=False)

    # 初始化GAT模型
    model = GAT(num_node_features=num_node_features,
                hidden_channels=64,
                num_classes=2,
                heads=8,
                dropout=0.2).to(device)

    # 初始化优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    criterion = torch.nn.CrossEntropyLoss()
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='max', factor=0.5, patience=20, verbose=True)

    # 训练模型
    best_val_acc = 0
    epochs = 500
    patience = 50
    patience_counter = 0

    print("\n开始训练GAT模型...")
    for epoch in range(1, epochs + 1):
        # 训练
        loss = train_model(model, train_loader, optimizer, criterion, device)

        # 评估
        train_acc, train_f1, _, _ = evaluate_model(model, train_loader, device)
        val_acc, val_f1, _, _ = evaluate_model(model, val_loader, device)

        # 学习率调度
        scheduler.step(val_acc)

        if epoch % 10 == 0:
            print(f'Epoch: {epoch:03d}, Loss: {loss:.4f}, Train Acc: {train_acc:.4f}, Val Acc: {val_acc:.4f}, Val F1: {val_f1:.4f}, LR: {optimizer.param_groups[0]["lr"]:.6f}')

        # 保存最佳模型和早停
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), 'best_gat_model.pt')
            patience_counter = 0
        else:
            patience_counter += 1

        # 早停
        if patience_counter >= patience:
            print(f'早停在第 {epoch} 轮，最佳验证准确率: {best_val_acc:.4f}')
            break

    # 加载最佳模型并在测试集上评估
    print("\n加载最佳GAT模型并在测试集上评估...")
    model.load_state_dict(torch.load('best_gat_model.pt'))

    # 最终评估
    train_acc, train_f1, _, _ = evaluate_model(model, train_loader, device)
    val_acc, val_f1, _, _ = evaluate_model(model, val_loader, device)
    test_acc, test_f1, test_preds, test_targets = evaluate_model(model, test_loader, device)

    print(f'\n最终结果:')
    print(f'训练集准确率: {train_acc:.4f}, F1分数: {train_f1:.4f}')
    print(f'验证集准确率: {val_acc:.4f}, F1分数: {val_f1:.4f}')
    print(f'测试集准确率: {test_acc:.4f}, F1分数: {test_f1:.4f}')

    # 保存测试集预测结果
    test_subject_results = []
    for i in range(len(test_subject_ids)):
        test_subject_results.append({
            'subject_id': test_subject_ids[i],
            'true_label': test_labels[i],
            'predicted_label': test_preds[i]
        })

    results_df = pd.DataFrame(test_subject_results)
    results_df.to_csv('gat_test_predictions.csv', index=False)
    print(f'测试集预测结果已保存到 gat_test_predictions.csv')

    # 测试集混淆矩阵
    cm = confusion_matrix(test_targets, test_preds)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=['HC', 'MCI'],
                yticklabels=['HC', 'MCI'])
    plt.xlabel('Predict labels')
    plt.ylabel('True labels')
    plt.title('GAT Test Set Confusion Matrix')
    plt.savefig('gat_test_confusion_matrix.png')
    plt.close()

    return test_acc, test_f1

def main():
    print("开始训练/验证/测试GAT模型...")
    test_acc, test_f1 = train_val_test_split()

if __name__ == "__main__":
    main()
