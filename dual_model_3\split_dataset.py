#!/usr/bin/env python3
"""
数据集划分脚本
将HC和MCI组的功能和结构数据按8:1:1的比例划分为训练集、验证集、测试集
"""

import os
import shutil
import random
import re
from pathlib import Path
from typing import List, Tuple, Dict

def extract_subject_id(filename: str) -> str:
    """从文件名中提取被试ID"""
    # 对于边列表文件: 01001_edges.csv -> 01001
    # 对于节点特征文件: ROISignals_01001.txt -> 01001
    if filename.startswith('ROISignals_'):
        match = re.search(r'ROISignals_(\d+)', filename)
    else:
        match = re.search(r'(\d+)_', filename)
    
    if match:
        return match.group(1)
    return None

def get_subject_files(group_path: str, data_type: str) -> Dict[str, List[str]]:
    """获取每个被试的所有文件"""
    subject_files = {}
    
    if not os.path.exists(group_path):
        print(f"警告: 路径不存在 {group_path}")
        return subject_files
    
    for filename in os.listdir(group_path):
        if os.path.isfile(os.path.join(group_path, filename)):
            subject_id = extract_subject_id(filename)
            if subject_id:
                if subject_id not in subject_files:
                    subject_files[subject_id] = []
                subject_files[subject_id].append(filename)
    
    return subject_files

def split_subjects(subject_ids: List[str], train_ratio: float = 0.8, 
                  val_ratio: float = 0.1, test_ratio: float = 0.1) -> Tuple[List[str], List[str], List[str]]:
    """按比例划分被试ID"""
    # 确保比例之和为1
    total_ratio = train_ratio + val_ratio + test_ratio
    if abs(total_ratio - 1.0) > 1e-6:
        raise ValueError(f"比例之和必须为1，当前为{total_ratio}")
    
    # 随机打乱被试ID
    random.shuffle(subject_ids)
    
    n_total = len(subject_ids)
    n_train = int(n_total * train_ratio)
    n_val = int(n_total * val_ratio)
    
    train_subjects = subject_ids[:n_train]
    val_subjects = subject_ids[n_train:n_train + n_val]
    test_subjects = subject_ids[n_train + n_val:]
    
    return train_subjects, val_subjects, test_subjects

def create_directories(base_path: str):
    """创建数据集目录结构"""
    splits = ['train', 'val', 'test']
    groups = ['HC', 'MCI']
    data_types = ['edge_lists', 'node_features']
    
    for split in splits:
        for data_type in data_types:
            for group in groups:
                dir_path = os.path.join(base_path, split, data_type, group)
                os.makedirs(dir_path, exist_ok=True)
                print(f"创建目录: {dir_path}")

def copy_files(source_base: str, target_base: str, group: str, data_type: str, 
               subject_files: Dict[str, List[str]], subjects: List[str], split_name: str):
    """复制文件到目标目录"""
    source_dir = os.path.join(source_base, data_type, group)
    target_dir = os.path.join(target_base, split_name, data_type, group)
    
    copied_count = 0
    for subject_id in subjects:
        if subject_id in subject_files:
            for filename in subject_files[subject_id]:
                source_path = os.path.join(source_dir, filename)
                target_path = os.path.join(target_dir, filename)
                
                if os.path.exists(source_path):
                    shutil.copy2(source_path, target_path)
                    copied_count += 1
                else:
                    print(f"警告: 源文件不存在 {source_path}")
    
    print(f"  {group} {data_type}: 复制了 {copied_count} 个文件")

def main():
    """主函数"""
    # 设置随机种子以确保结果可重现
    random.seed(39)
    
    # 基础路径
    base_path = "."
    output_path = "dataset_splits"
    
    # 数据类型和组别
    data_types = ['edge_lists', 'node_features']
    groups = ['HC', 'MCI']
    
    print("开始数据集划分...")
    print("=" * 50)
    
    # 创建输出目录结构
    create_directories(output_path)
    
    # 处理每个组别
    for group in groups:
        print(f"\n处理 {group} 组:")
        print("-" * 30)
        
        # 获取边列表和节点特征的被试文件
        edge_subject_files = get_subject_files(os.path.join(base_path, 'edge_lists', group), 'edge_lists')
        node_subject_files = get_subject_files(os.path.join(base_path, 'node_features', group), 'node_features')
        
        # 获取所有被试ID（取两种数据类型的交集）
        edge_subjects = set(edge_subject_files.keys())
        node_subjects = set(node_subject_files.keys())
        common_subjects = list(edge_subjects.intersection(node_subjects))
        
        print(f"边列表文件被试数: {len(edge_subjects)}")
        print(f"节点特征文件被试数: {len(node_subjects)}")
        print(f"共同被试数: {len(common_subjects)}")
        
        if len(common_subjects) == 0:
            print(f"警告: {group} 组没有找到共同的被试")
            continue
        
        # 划分被试
        train_subjects, val_subjects, test_subjects = split_subjects(common_subjects)
        
        print(f"训练集被试数: {len(train_subjects)} ({len(train_subjects)/len(common_subjects)*100:.1f}%)")
        print(f"验证集被试数: {len(val_subjects)} ({len(val_subjects)/len(common_subjects)*100:.1f}%)")
        print(f"测试集被试数: {len(test_subjects)} ({len(test_subjects)/len(common_subjects)*100:.1f}%)")
        
        # 复制文件
        splits_data = [
            ('train', train_subjects),
            ('val', val_subjects),
            ('test', test_subjects)
        ]
        
        for split_name, subjects in splits_data:
            print(f"\n复制 {split_name} 集文件:")
            # 复制边列表文件
            copy_files(base_path, output_path, group, 'edge_lists', 
                      edge_subject_files, subjects, split_name)
            # 复制节点特征文件
            copy_files(base_path, output_path, group, 'node_features', 
                      node_subject_files, subjects, split_name)
    
    print("\n" + "=" * 50)
    print("数据集划分完成!")
    
    # 统计最终结果
    print("\n最终统计:")
    for split in ['train', 'val', 'test']:
        print(f"\n{split.upper()} 集:")
        for group in groups:
            for data_type in data_types:
                split_dir = os.path.join(output_path, split, data_type, group)
                if os.path.exists(split_dir):
                    file_count = len([f for f in os.listdir(split_dir) if os.path.isfile(os.path.join(split_dir, f))])
                    print(f"  {group} {data_type}: {file_count} 个文件")

if __name__ == "__main__":
    main()
